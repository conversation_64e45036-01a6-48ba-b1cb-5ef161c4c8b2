'use client';

import { AlertCircle, CheckCircle, FileText, Home } from 'lucide-react';
import React, { useMemo, useState } from 'react';
import { useDispatch } from 'react-redux';
import { useRouter } from 'next/navigation';

import UploadFile from '@/components/home/<USER>/uploadNewFile';
import { Button } from '@/components/ui/Button';
import { useGenProWorkflow } from '@/contexts/GenProWorkflowContext';
import { GenProService } from '@/service/genpro.service';
import { APIService } from '@/service/api.service';
import { setIsLoading } from '@/slices/appSlice';
import { setToastAlert } from '@/slices/metaDataSlice';

interface IngestionProps {
  onNext?: () => void;
  onPrevious?: () => void;
}

const Ingestion = ({ onNext }: IngestionProps) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const apiService = useMemo(() => new APIService(dispatch, router), []);
  const genproService = useMemo(() => new GenProService(apiService), [apiService]);

  const { setUploadedFiles } = useGenProWorkflow();

  const [fileUploaded, setFileUploaded] = useState(false);
  const [, setDroppedFiles] = useState<any>([]);
  const [uploadedFileData, setUploadedFileData] = useState<any>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [isUploadCancelled, setIsUploadCancelled] = useState(false);
  
  const MINIMUM_FILES_REQUIRED = 3;
  const MAXIMUM_FILES_ALLOWED = 3;

  const handleFiles = async (files: any) => {
    // When real files are uploaded, replace dummy files completely
    const totalFilesAfterUpload = files.length;
    
    if (totalFilesAfterUpload > MAXIMUM_FILES_ALLOWED) {
      dispatch(setToastAlert({
        isToastOpen: true,
        intent: 'error',
        title: 'Too Many Files',
        content: `Maximum ${MAXIMUM_FILES_ALLOWED} files allowed. Cannot upload ${files.length} file(s).`,
      }));
      return;
    }

    setDroppedFiles(files);
    setIsUploading(true);
    dispatch(setIsLoading(true));

    try {
      

      setUploadedFileData(fileData
      setUploadedFiles(fileData); // Update context
      setFileUploaded(fileData.length >= MINIMUM_FILES_REQUIRED && fileData.length <= MAXIMUM_FILES_ALLOWED);
    } catch (error: any) {
      // eslint-disable-next-line no-console
      console.error('Upload failed:', error);
      dispatch(
        setToastAlert({
          isToastOpen: true,
          intent: 'error',
          title: 'Upload Failed',
          content: 'Failed to process files',
        }),
      );
    } finally {
      setIsUploading(false);
      dispatch(setIsLoading(false));
    }
  };

  const handelClear = () => {
    setFileUploaded(false);
    setDroppedFiles([]);
    setUploadedFileData([]);
    setUploadedFiles([]); // Clear context
    setIsUploadCancelled(true); // Trigger file upload component to clear files
    
    // Reset the cancel state after a brief delay to allow the upload component to process
    setTimeout(() => {
      setIsUploadCancelled(false);
    }, 100);
  };



  const handleProceed = async () => {
    
    if (uploadedFileData.length < MINIMUM_FILES_REQUIRED) {
      dispatch(setToastAlert({
        isToastOpen: true,
        intent: 'warning',
        title: 'Insufficient Files',
        content: `Please upload at least ${MINIMUM_FILES_REQUIRED} files to proceed. Currently have ${uploadedFileData.length}/${MINIMUM_FILES_REQUIRED} files.`,
      }));
      return;
    }

    if (uploadedFileData.length > MAXIMUM_FILES_ALLOWED) {
      dispatch(setToastAlert({
        isToastOpen: true,
        intent: 'error',
        title: 'Too Many Files',
        content: `Maximum ${MAXIMUM_FILES_ALLOWED} files allowed. Please remove ${uploadedFileData.length - MAXIMUM_FILES_ALLOWED} file(s) before proceeding.`,
      }));
      return;
    }
    
    setIsLoading(true)
    
    try {
      const rawFiles = uploadedFileData
        .filter((fileData: any) => fileData.rawFile)
        .map((fileData: any) => fileData.rawFile);
      
      if (rawFiles.length > 0) {
        const response = await genproService.uploadWorkflowFiles(rawFiles, false);
        
        dispatch(setToastAlert({
          isToastOpen: true,
          intent: 'success',
          title: 'Files Uploaded Successfully',
          content: 'Files have been uploaded to the workflow system.',
        }));
        
        // Update uploaded files with response data if available
        if (response.data) {
          setUploadedFiles(response.data);
        }
      }
      
      onNext?.();
    } catch (error: any) {
      dispatch(setToastAlert({
        isToastOpen: true,
        intent: 'error',
        title: 'Upload Failed',
        content: error.response?.data?.error || 'Failed to upload files to workflow system',
      }));
    } finally {
      dispatch(setIsLoading(false));
    }
  };

  return (
    <div className="h-full flex flex-col overflow-hidden p-4">
      {/* Main Upload Area - Flex with controlled height */}
      <div className="mb-3 flex min-h-0 flex-1 flex-col rounded border border-lightgray-100 bg-white-200">
        <div className="flex-1 overflow-hidden p-3">
          <UploadFile onFileUpload={handleFiles} isUploadCancled={isUploadCancelled} />
          {isUploading && (
            <div className="mt-3 rounded border bg-blue-50 p-3">
              <div className="flex items-center space-x-2">
                <div className="size-4 animate-spin rounded-full border-b-2 border-blue-600" />
                <span className="text-sm text-blue-600">
                  Uploading files to GenPro...
                </span>
              </div>
            </div>
          )}
          
        </div>
      </div>

      {/* Navigation Controls - Fixed Footer */}
      <div className="shrink-0 rounded border border-lightgray-100 bg-white-200 p-2">
        <div className="flex items-center justify-between">
          {/* Status Section */}
          <div className="flex items-center space-x-3">
            {uploadedFileData.length >= MINIMUM_FILES_REQUIRED && uploadedFileData.length <= MAXIMUM_FILES_ALLOWED ? (
              <CheckCircle className="size-4 text-green-500" />
            ) : (
              <AlertCircle className="size-4 text-orange-500" />
            )}
            <span className={`text-xs font-medium ${
              uploadedFileData.length >= MINIMUM_FILES_REQUIRED && uploadedFileData.length <= MAXIMUM_FILES_ALLOWED ? 'text-green-600' : 'text-orange-600'
            }`}>
              {uploadedFileData.length >= MINIMUM_FILES_REQUIRED && uploadedFileData.length <= MAXIMUM_FILES_ALLOWED
                ? `${uploadedFileData.length} files attached ✓` 
                : uploadedFileData.length > MAXIMUM_FILES_ALLOWED
                  ? `${uploadedFileData.length} files attached (Max ${MAXIMUM_FILES_ALLOWED})`
                  : `${uploadedFileData.length}/${MINIMUM_FILES_REQUIRED} files attached`
              }
            </span>
            {uploadedFileData.length < MINIMUM_FILES_REQUIRED && (
              <>
                <span className="text-xs text-gray-400">•</span>
                <span className="text-xs text-orange-600">
                  Need {MINIMUM_FILES_REQUIRED - uploadedFileData.length} more file(s)
                </span>
              </>
            )}
            {uploadedFileData.length > MAXIMUM_FILES_ALLOWED && (
              <>
                <span className="text-xs text-gray-400">•</span>
                <span className="text-xs text-red-600">
                  Remove {uploadedFileData.length - MAXIMUM_FILES_ALLOWED} file(s)
                </span>
              </>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex space-x-2">
            {uploadedFileData.length > 0 && (
              <Button
                className="flex h-[32px] items-center px-3 text-xs"
                type="button"
                onClick={handelClear}
                intent="secondary"
              >
                Clear Files
              </Button>
            )}
            <Button
              className="flex h-[32px] items-center bg-blue-600 px-4 text-xs font-semibold text-white-200"
              type="submit"
              disabled={uploadedFileData.length < MINIMUM_FILES_REQUIRED || uploadedFileData.length > MAXIMUM_FILES_ALLOWED}
              onClick={handleProceed}
              aria-label={
                uploadedFileData.length < MINIMUM_FILES_REQUIRED
                  ? `Upload ${MINIMUM_FILES_REQUIRED - uploadedFileData.length} more file(s) to continue`
                  : uploadedFileData.length > MAXIMUM_FILES_ALLOWED
                    ? `Remove ${uploadedFileData.length - MAXIMUM_FILES_ALLOWED} file(s) to continue`
                    : 'Proceed to validation step'
              }
            >
              {uploadedFileData.length < MINIMUM_FILES_REQUIRED
                ? `Upload ${MINIMUM_FILES_REQUIRED - uploadedFileData.length} More File(s)`
                : uploadedFileData.length > MAXIMUM_FILES_ALLOWED
                  ? `Remove ${uploadedFileData.length - MAXIMUM_FILES_ALLOWED} File(s)`
                  : 'Proceed to Validation →'}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Ingestion;