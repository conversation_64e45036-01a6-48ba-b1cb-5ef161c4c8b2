"use client";

import React, { useState, useEffect, useMemo } from "react";
import { Plus, Edit, Trash2, Loader2 } from "lucide-react";
import { useRouter } from "next/navigation";
import { useDispatch } from "react-redux";

import { Button } from "@/components/ui/Button";
import { Modal } from "@/components/ui/Modal";
import { V2InputField } from "@/components/ui/V2InputField";
import V2SelectBox from "@/components/ui/V2SelectBox";
import V2DataGrid from "@/components/ui/V2DataGrid";
import V2MUIDropdown from "@/components/ui/V2MUIDropdown";
import { APIService } from "@/service/api.service";
import { setToastAlert, setWarningAlert } from "@/slices/metaDataSlice";
import { WarningDialog } from "../ui/Dialog";
import { ApiUtilities } from "@/utils/ApiUtilities";

interface SMC {
  id: number;
  smc_name: string;
  is_active: boolean;
}


type TabType = "SMC" | "Entity";

const LoadingSpinner = ({ text }: { text: string }) => (
  <div className="p-4 flex items-center justify-center h-48">
    <div className="flex items-center space-x-2">
      <Loader2 className="w-6 h-6 animate-spin text-blue-600" />
      <span className="text-gray-600">{text}</span>
    </div>
  </div>
);

const VesselForm = ({ formData, onFormChange, smcOptions, rawSmcData, isLoading }) => {
  if (isLoading) return <LoadingSpinner text="Loading data..." />;

  return (
    <div className="p-4 space-y-4">
      <V2InputField
        label="Entity Code"
        type="text"
        placeholder="Enter entity code"
        onChange={(e) => onFormChange("entity_code", e.target.value)}
        value={formData.entity_code}
      />
      <V2InputField
        label="Vessel Name"
        type="text"
        placeholder="Enter vessel name"
        onChange={(e) => onFormChange("vessel_name", e.target.value)}
        value={formData.vessel_name}
      />
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">SMC</label>
        <V2SelectBox
          options={smcOptions}
          name="smc"
          placeholder="Select SMC"
          value={rawSmcData.find((smc) => smc.id.toString() === formData.smc)?.smc_name || ""}
          onChange={(event) => {
            const selectedSmc = rawSmcData.find((smc) => smc.smc_name === event.target.value);
            onFormChange("smc", selectedSmc?.id.toString() || "");
          }}
        />
      </div>
    </div>
  );
};

const SmcForm = ({ formData, onFormChange, isLoading }) => {
  if (isLoading) return <LoadingSpinner text="Loading SMC data..." />;

  return (
    <div className="p-4 space-y-4">
      <V2InputField
        label="SMC Name"
        type="text"
        placeholder="Enter SMC name"
        onChange={(e) => onFormChange("smc_name", e.target.value)}
        value={formData.smc_name}
      />
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
        <V2SelectBox
          options={["Active", "Inactive"]}
          name="is_active"
          placeholder="Select Status"
          value={formData.is_active ? "Active" : "Inactive"}
          onChange={(event) => onFormChange("is_active", event.target.value === "Active")}
        />
      </div>
    </div>
  );
};

const MappingTable = () => {
  const router = useRouter();
  const dispatch = useDispatch();
  const apiService = useMemo(() => new APIService(dispatch, router), [dispatch, router]);

  // State
  const [activeTab, setActiveTab] = useState<TabType>("Entity");
  const [smcData, setSmcData] = useState<any[]>([]);
  const [rawSmcData, setRawSmcData] = useState<SMC[]>([]);
  const [vesselData, setVesselData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Modal states
  const [modals, setModals] = useState({
    vesselEdit: false,
    vesselCreate: false,
    smcEdit: false,
    smcCreate: false,
  });

  // Form data
  const [vesselForm, setVesselForm] = useState({ entity_code: "", vessel_name: "", smc: "" });
  const [smcForm, setSmcForm] = useState({ smc_name: "", is_active: true });
  const [editingId, setEditingId] = useState<number | null>(null);
  const [deletingId, setDeletingId] = useState<number | null>(null);
  const [formLoading, setFormLoading] = useState(false);
  const [saveLoading, setSaveLoading] = useState(false);

  // API Functions
  const getAllSmcs = async () => {
    const url = `${ApiUtilities.getApiServerUrlGenPro}${ApiUtilities.apiPath.genproSmc.url}`;
    const response = await apiService.genproGetRequest(url);
    return response.data;
  };

  const getSmcById = async (id: number) => {
    const url = `${ApiUtilities.getApiServerUrlGenPro}${ApiUtilities.apiPath.genproSmc.url}/${id}`;
    const response = await apiService.genproGetRequest(url);
    return response.data;
  };

  const createSmc = async (data: any) => {
    const url = `${ApiUtilities.getApiServerUrlGenPro}${ApiUtilities.apiPath.genproSmcCreate.url}`;
    return await apiService.genproPostRequest(url, data);
  };

  const updateSmc = async (id: number, data: any) => {
    const url = `${ApiUtilities.getApiServerUrlGenPro}${ApiUtilities.apiPath.genproSmcUpdate.url.replace('{id}', id.toString())}`;
    return await apiService.genproPutRequest(url, data);
  };

  const deleteSmc = async (id: number) => {
    const url = `${ApiUtilities.getApiServerUrlGenPro}${ApiUtilities.apiPath.genproSmcDelete.url.replace('{id}', id.toString())}`;
    return await apiService.genproDeleteRequest(url);
  };

  const getAllVessels = async () => {
    const url = `${ApiUtilities.getApiServerUrlGenPro}${ApiUtilities.apiPath.genproVesselEntityActive.url}`;
    const response = await apiService.genproGetRequest(url);
    return response.data;
  };

  const getVesselById = async (id: number) => {
    const url = `${ApiUtilities.getApiServerUrlGenPro}${ApiUtilities.apiPath.genproVesselEntity.url}${id}`;
    const response = await apiService.genproGetRequest(url);
    return response.data;
  };

  const createVessel = async (data: any) => {
    const url = `${ApiUtilities.getApiServerUrlGenPro}${ApiUtilities.apiPath.genproVesselEntity.url}`;
    return await apiService.genproPostRequest(url, data);
  };

  const updateVessel = async (id: number, data: any) => {
    const url = `${ApiUtilities.getApiServerUrlGenPro}${ApiUtilities.apiPath.genproVesselEntityUpdate.url.replace('{id}', id.toString())}`;
    return await apiService.genproPutRequest(url, data);
  };

  const deleteVessel = async (id: number) => {
    const url = `${ApiUtilities.getApiServerUrlGenPro}${ApiUtilities.apiPath.genproVesselEntityDelete.url.replace('{id}', id.toString())}`;
    return await apiService.genproDeleteRequest(url);
  };

  // Data fetching
  const loadSmcs = async () => {
    setLoading(true);
    setError(null);
    try {
      const data = await getAllSmcs();
      setRawSmcData(data.results);
      
      const formattedData = data.results.map((item, index) => ({
        id: item.id,
        SNO: index + 1,
        "SMC NAME": item.smc_name,
        is_active: item.is_active,
        STATUS: (
          <span className={`inline-flex items-center px-1 rounded-full text-xxs font-medium leading-none ${
            item.is_active ? "bg-green-50 text-green-700 border border-green-200" : "bg-red-100 text-red-800"
          }`}>
            {item.is_active ? "Active" : "Inactive"}
          </span>
        ),
      }));
      setSmcData(formattedData);
    } catch (error: any) {
      setError(error.message || "Failed to fetch SMC data");
      showError("Failed to fetch SMC data", error);
    } finally {
      setLoading(false);
    }
  };

  const loadVessels = async () => {
    setLoading(true);
    setError(null);
    try {
      const data = await getAllVessels();
      const formattedData = data.map((item, index) => ({
        id: item.id,
        SNO: index + 1,
        "ENTITY CODE": item.entity_code?.toUpperCase(),
        "VESSEL NAME": item.vessel_name,
        "SMC NAME": item.smc?.smc_name || "N/A",
        is_active: item.is_active,
        created_at: item.created_at,
        smc: item.smc,
        STATUS: (
          <span className={`inline-flex items-center px-1 rounded-full text-xxs font-medium leading-none ${
            item.is_active ? "bg-green-50 text-green-700 border border-green-200" : "bg-red-100 text-red-800"
          }`}>
            {item.is_active ? "Active" : "Inactive"}
          </span>
        ),
        "CREATED DATE": new Date(item.created_at).toLocaleDateString(),
      }));
      setVesselData(formattedData);
    } catch (error: any) {
      setError(error.message || "Failed to fetch vessel data");
      showError("Failed to fetch vessel data", error);
    } finally {
      setLoading(false);
    }
  };

  // Utility functions
  const showSuccess = (message: string) => {
    dispatch(setToastAlert({ isToastOpen: true, intent: "success", title: "Success", content: message }));
  };

  const showError = (message: string, error?: any) => {
    const errorMessage = error?.response?.data?.message || error?.message || message;
    dispatch(setToastAlert({ isToastOpen: true, intent: "error", title: "Error", content: errorMessage }));
  };

  const closeWarningDialog = () => {
    dispatch(setWarningAlert({ isWarningOpen: false, headerTitle: "", message: "", actionbuttonText: "", cancelButtonText: "" }));
  };

  const resetForms = () => {
    setVesselForm({ entity_code: "", vessel_name: "", smc: "" });
    setSmcForm({ smc_name: "", is_active: true });
    setEditingId(null);
  };

  const openModal = (type: string) => {
    setModals(prev => ({ ...prev, [type]: true }));
  };

  const closeModal = (type: string) => {
    setModals(prev => ({ ...prev, [type]: false }));
    resetForms();
  };

  // Event handlers
  const onEdit = async (id: number) => {
    if (activeTab === "Entity") {
      openModal("vesselEdit");
      setFormLoading(true);
      resetForms();

      try {
        const [vesselData] = await Promise.all([
          getVesselById(id),
          rawSmcData.length === 0 ? loadSmcs() : Promise.resolve()
        ]);

        if (vesselData) {
          setEditingId(id);
          setVesselForm({
            entity_code: vesselData.entity_code || "",
            vessel_name: vesselData.vessel_name || "",
            smc: vesselData.smc?.id?.toString() || "",
          });
        }
      } catch (error) {
        showError("Failed to fetch vessel data", error);
      } finally {
        setFormLoading(false);
      }
    } else {
      openModal("smcEdit");
      setFormLoading(true);
      resetForms();

      try {
        const smcData = await getSmcById(id);
        if (smcData) {
          setEditingId(id);
          setSmcForm({ smc_name: smcData.smc_name || "", is_active: smcData.is_active });
        }
      } catch (error) {
        showError("Failed to fetch SMC data", error);
      } finally {
        setFormLoading(false);
      }
    }
  };

  const onDelete = (id: number) => {
    setDeletingId(id);
    const itemName = activeTab === "Entity" 
      ? vesselData.find(item => item.id === id)?.["VESSEL NAME"]
      : smcData.find(item => item.id === id)?.["SMC NAME"];

    dispatch(setWarningAlert({
      isWarningOpen: true,
      headerTitle: `Delete ${activeTab}`,
      message: `Are you sure you want to delete "${itemName}"? This action cannot be undone.`,
      actionbuttonText: "Yes, Delete",
      cancelButtonText: "No, Cancel",
    }));
  };

  const onConfirmDelete = async () => {
    if (!deletingId) return;

    try {
      if (activeTab === "Entity") {
        await deleteVessel(deletingId);
        showSuccess("Vessel entity deleted successfully!");
        loadVessels();
      } else {
        await deleteSmc(deletingId);
        showSuccess("SMC deleted successfully!");
        loadSmcs();
      }
    } catch (error) {
      showError(`Failed to delete ${activeTab.toLowerCase()}`, error);
    } finally {
      setDeletingId(null);
      closeWarningDialog();
    }
  };

  const onSave = async () => {
    if (!editingId) return;
    setSaveLoading(true);

    try {
      if (activeTab === "Entity") {
        const data = { ...vesselForm, smc_id: parseInt(vesselForm.smc) };
        await updateVessel(editingId, data);
        showSuccess("Vessel entity updated successfully!");
        closeModal("vesselEdit");
        loadVessels();
      } else {
        await updateSmc(editingId, smcForm);
        showSuccess("SMC updated successfully!");
        closeModal("smcEdit");
        loadSmcs();
      }
    } catch (error) {
      showError(`Failed to update ${activeTab.toLowerCase()}`, error);
    } finally {
      setSaveLoading(false);
    }
  };

  const onCreate = async () => {
    setSaveLoading(true);

    try {
      if (activeTab === "Entity") {
        const data = { ...vesselForm, smc_id: parseInt(vesselForm.smc) };
        await createVessel(data);
        showSuccess("Vessel entity created successfully!");
        closeModal("vesselCreate");
        loadVessels();
      } else {
        await createSmc(smcForm);
        showSuccess("SMC created successfully!");
        closeModal("smcCreate");
        loadSmcs();
      }
    } catch (error) {
      showError(`Failed to create ${activeTab.toLowerCase()}`, error);
    } finally {
      setSaveLoading(false);
    }
  };

  const onAdd = async () => {
    if (activeTab === "Entity") {
      openModal("vesselCreate");
      if (rawSmcData.length === 0) {
        setFormLoading(true);
        try {
          await loadSmcs();
        } finally {
          setFormLoading(false);
        }
      }
    } else {
      openModal("smcCreate");
    }
  };

  // Load data on component mount and tab change
  useEffect(() => {
    if (activeTab === "SMC") loadSmcs();
    else loadVessels();
  }, [activeTab]);

  // Load both datasets on component mount to show counts
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        await Promise.all([loadSmcs(), loadVessels()]);
      } catch (error) {
        console.error('Failed to load initial data:', error);
      }
    };
    
    loadInitialData();
  }, []);

  // Table configuration
  const menuData = [
    {
      option: (
        <div className="flex items-center gap-2">
          <Edit className="w-3 h-3" />
          <p className="text-gray-700 text-sm">Edit</p>
        </div>
      ),
      clickFn: onEdit,
    },
    {
      option: (
        <div className="flex items-center gap-2">
          <Trash2 className="w-3 h-3" />
          <p className="text-red-600 text-sm">Delete</p>
        </div>
      ),
      clickFn: onDelete,
    },
  ];

  const actionColumn = {
    key: "actions",
    name: "ACTIONS",
    width: 120,
    filterType: "",
    renderCell: ({ row }: any) => (
      <div className="flex justify-center items-center h-full cursor-pointer">
        <V2MUIDropdown menuData={menuData} currentData={row.id} menuIcon={
          <div className="flex items-center justify-center w-6 h-6">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" className="text-gray-600">
              <circle cx="12" cy="5" r="2" fill="currentColor" />
              <circle cx="12" cy="12" r="2" fill="currentColor" />
              <circle cx="12" cy="19" r="2" fill="currentColor" />
            </svg>
          </div>
        } />
      </div>
    ),
  };

  const smcHeaders = [
    { key: "sno", name: "SNO", width: 50, filterType: "" },
    { key: "smc_name", name: "SMC NAME", width: 400, isSortable: false, filterType: "" },
    { key: "status", name: "STATUS", width: 150, isSortable: false, filterType: "" },
    actionColumn,
  ];

  const vesselHeaders = [
    { key: "sno", name: "SNO", width: 100, isSortable: false, isFrozen: true, filterType: "" },
    { key: "entity_code", name: "ENTITY CODE", width: 160, isSortable: false, filterType: "" },
    { key: "vessel_name", name: "VESSEL NAME", width: 250, isSortable: false, filterType: "" },
    { key: "smc_name", name: "SMC NAME", width: 200, isSortable: false, filterType: "" },
    { key: "status", name: "STATUS", width: 150, isSortable: false, filterType: "" },
    { key: "created_date", name: "CREATED DATE", width: 150, isSortable: false, filterType: "" },
    actionColumn,
  ];

  const currentData = activeTab === "SMC" ? smcData : vesselData;
  const currentHeaders = activeTab === "SMC" ? smcHeaders : vesselHeaders;
  const smcOptions = rawSmcData.filter(smc => smc.is_active).map(smc => smc.smc_name);

  return (
    <div className="h-[calc(100vh-64px)] flex flex-col bg-white">
      <div className="flex-1 flex flex-col px-6 py-2 overflow-hidden">
        {/* Tabs */}
        <div className="mb-4">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {(["Entity", "SMC"] as TabType[]).map((tab) => (
                <button
                  key={tab}
                  onClick={() => setActiveTab(tab)}
                  className={`py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab
                      ? "border-blue-500 text-blue-600"
                      : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                  }`}
                >
                  {tab}
                  {((tab === "SMC" && smcData.length > 0) || (tab === "Entity" && vesselData.length > 0)) && (
                    <span className="ml-2 text-gray-900 py-0.5 px-2 rounded-full text-xs">
                      {tab === "SMC" ? smcData.length : vesselData.length}
                    </span>
                  )}
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 bg-white min-h-0">
          {loading ? (
            <div className="flex items-center justify-center h-full w-full">
              <LoadingSpinner text={`Loading ${activeTab.toLowerCase()} data...`} />
            </div>
          ) : error ? (
            <div className="flex items-center justify-center h-full w-full">
              <div className="text-center">
                <div className="text-red-600 mb-2">Failed to load {activeTab.toLowerCase()} data</div>
                <Button onClick={() => activeTab === "SMC" ? loadSmcs() : loadVessels()} intent="secondary" className="text-sm px-3 py-1">
                  Retry
                </Button>
              </div>
            </div>
          ) : (
            <div className="flex h-full gap-6">
              <div className="h-full flex flex-col">
                <div className="flex justify-end mb-3 flex-shrink-0">
                  <Button onClick={onAdd} className="flex justify-center items-center text-sm px-3">
                    <Plus className="w-3 h-3 mr-2" />
                    Add {activeTab}
                  </Button>
                </div>
                <div className="h-[70%] overflow-auto">
                  <V2DataGrid data={currentData} headerList={currentHeaders} handleRowSelected={() => {}} />
                </div>
              </div>

              {/* SMC Notes */}
              {activeTab === "SMC" && (
                <div className="w-[30%] h-[70%] flex flex-col">
                  <div className="bg-blue-50 rounded-lg p-4 border border-blue-100 h-full">
                    <h3 className="text-sm font-semibold text-gray-800 mb-3">📋 SMC Management Note</h3>
                    <div className="text-xs text-gray-700 space-y-3">
                      <div>
                        <h4 className="font-medium text-gray-800 mb-1">About SMC Entities</h4>
                        <p>Ship Management Companies (SMCs) are the primary entities responsible for vessel operations and management.</p>
                      </div>
                      <div>
                        <h4 className="font-medium text-gray-800 mb-1">Key Guidelines</h4>
                        <ul className="space-y-1 ml-2">
                          <li>• Each SMC must have a unique name</li>
                          <li>• Active SMCs can be assigned to vessels</li>
                          <li>• Inactive SMCs are retained for historical data</li>
                          <li>• Use clear, descriptive names for easy identification</li>
                        </ul>
                      </div>
                      <div className="mt-4 p-2 bg-yellow-50 border border-yellow-200 rounded">
                        <p className="text-xs text-yellow-800">
                          <strong>Tip:</strong> Before deactivating an SMC, ensure no active vessels are assigned to it.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Modals */}
      <Modal
        isOpen={modals.vesselEdit}
        closeModal={() => closeModal("vesselEdit")}
        headerTitle="Edit Vessel Entity"
        component={<VesselForm formData={vesselForm} onFormChange={(field, value) => setVesselForm(prev => ({ ...prev, [field]: value }))} smcOptions={smcOptions} rawSmcData={rawSmcData} isLoading={formLoading} />}
        actionbuttonText="Save Changes"
        cancelbuttonText="Cancel"
        isActionButtonVisible={true}
        isCancelVisible={true}
        footerPrimaryEventHandler={onSave}
        footerSecondaryEventHandler={() => closeModal("vesselEdit")}
        disablePrimaryFooterBtn={saveLoading || !vesselForm.entity_code || !vesselForm.vessel_name || !vesselForm.smc}
        panelWidth="w-[500px]"
      />

      <Modal
        isOpen={modals.vesselCreate}
        closeModal={() => closeModal("vesselCreate")}
        headerTitle="Create Vessel Entity"
        component={<VesselForm formData={vesselForm} onFormChange={(field, value) => setVesselForm(prev => ({ ...prev, [field]: value }))} smcOptions={smcOptions} rawSmcData={rawSmcData} isLoading={formLoading} />}
        actionbuttonText="Create Entity"
        cancelbuttonText="Cancel"
        isActionButtonVisible={true}
        isCancelVisible={true}
        footerPrimaryEventHandler={onCreate}
        footerSecondaryEventHandler={() => closeModal("vesselCreate")}
        disablePrimaryFooterBtn={saveLoading || !vesselForm.entity_code || !vesselForm.vessel_name || !vesselForm.smc}
        panelWidth="w-[500px]"
      />

      <Modal
        isOpen={modals.smcEdit}
        closeModal={() => closeModal("smcEdit")}
        headerTitle="Edit SMC"
        component={<SmcForm formData={smcForm} onFormChange={(field, value) => setSmcForm(prev => ({ ...prev, [field]: value }))} isLoading={formLoading} />}
        actionbuttonText="Save Changes"
        cancelbuttonText="Cancel"
        isActionButtonVisible={true}
        isCancelVisible={true}
        footerPrimaryEventHandler={onSave}
        footerSecondaryEventHandler={() => closeModal("smcEdit")}
        disablePrimaryFooterBtn={saveLoading || !smcForm.smc_name}
        panelWidth="w-[500px]"
      />

      <Modal
        isOpen={modals.smcCreate}
        closeModal={() => closeModal("smcCreate")}
        headerTitle="Create SMC"
        component={<SmcForm formData={smcForm} onFormChange={(field, value) => setSmcForm(prev => ({ ...prev, [field]: value }))} isLoading={false} />}
        actionbuttonText="Create SMC"
        cancelbuttonText="Cancel"
        isActionButtonVisible={true}
        isCancelVisible={true}
        footerPrimaryEventHandler={onCreate}
        footerSecondaryEventHandler={() => closeModal("smcCreate")}
        disablePrimaryFooterBtn={saveLoading || !smcForm.smc_name}
        panelWidth="w-[500px]"
      />

      <WarningDialog onConfirm={onConfirmDelete} />
    </div>
  );
};

export default MappingTable;